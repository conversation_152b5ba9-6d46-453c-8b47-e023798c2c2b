# 虚拟实验室系统 - API接口文档

## 概述

虚拟实验室系统提供RESTful API接口，支持用户管理和化学实验模拟功能。所有API接口返回JSON格式数据。

### 基础信息
- **Base URL**: `http://localhost:8080`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式

#### 成功响应
```json
{
  "message": "操作成功信息",
  "data": "具体数据内容"
}
```

#### 错误响应
```json
{
  "error": "错误信息描述"
}
```

### HTTP状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 用户管理接口

### 1. 用户注册

注册新用户账号。

**接口地址**: `POST /api/users/register`

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
  "username": "string",  // 用户名，3-50个字符，必填
  "password": "string"   // 密码，至少6个字符，必填
}
```

**参数说明**:
- `username`: 用户名，支持字母、数字、下划线、中文字符
- `password`: 密码，建议包含字母和数字

**成功响应** (200):
```json
{
  "message": "Registration successful",
  "userId": 1
}
```

**错误响应**:

用户名已存在 (409):
```json
{
  "error": "User already exists"
}
```

参数验证失败 (400):
```json
{
  "error": "Username is required"
}
```

**示例请求**:
```bash
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

### 2. 用户登录

验证用户凭据并获取用户信息。

**接口地址**: `POST /api/users/login`

**请求头**:
```
Content-Type: application/json
```

**请求参数**:
```json
{
  "username": "string",  // 用户名，必填
  "password": "string"   // 密码，必填
}
```

**成功响应** (200):
```json
{
  "message": "Login successful",
  "userId": 1
}
```

**错误响应**:

凭据无效 (401):
```json
{
  "error": "Invalid credentials"
}
```

**示例请求**:
```bash
curl -X POST http://localhost:8080/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

## 实验管理接口

### 3. 运行化学实验

执行化学反应模拟并返回平衡方程式和安全警告。

**接口地址**: `POST /api/experiments/run`

**请求头**:
```
Content-Type: application/json
```

**查询参数**:
- `userId` (必填): 用户ID

**请求参数**:
```json
{
  "input": "string"  // 化学试剂输入，必填
}
```

**支持的化学反应**:
- `H2 + O2` - 氢气与氧气反应
- `Na + Cl2` - 钠与氯气反应
- `C + O2` - 碳与氧气反应
- `CH4 + O2` - 甲烷与氧气反应
- `HCl + NaOH` - 盐酸与氢氧化钠反应
- `Fe + O2` - 铁与氧气反应
- `Mg + O2` - 镁与氧气反应
- `NH3 + HCl` - 氨气与氯化氢反应

**成功响应** (200):
```json
{
  "result": "2H2 + O2 -> 2H2O",
  "warning": "High temperature may cause an explosion"
}
```

**错误响应**:

用户ID缺失 (400):
```json
{
  "error": "User ID is required"
}
```

输入为空 (400):
```json
{
  "error": "Input reactants are required"
}
```

反应未找到 (200):
```json
{
  "result": "Reaction not found in database: Unknown + Reaction",
  "warning": "Unknown reaction - please verify reactants and consult safety guidelines"
}
```

**示例请求**:
```bash
curl -X POST "http://localhost:8080/api/experiments/run?userId=1" \
  -H "Content-Type: application/json" \
  -d '{
    "input": "H2 + O2"
  }'
```

### 4. 查询实验历史

获取指定用户的实验历史记录。

**接口地址**: `GET /api/experiments`

**查询参数**:
- `userId` (必填): 用户ID

**成功响应** (200):
```json
[
  {
    "id": 1,
    "userId": 1,
    "inputData": "H2 + O2",
    "result": "2H2 + O2 -> 2H2O",
    "createdAt": "2025-08-25T19:31:50.649303"
  },
  {
    "id": 2,
    "userId": 1,
    "inputData": "Na + Cl2",
    "result": "2Na + Cl2 -> 2NaCl",
    "createdAt": "2025-08-25T19:30:15.123456"
  }
]
```

**响应字段说明**:
- `id`: 实验记录ID
- `userId`: 用户ID
- `inputData`: 输入的化学试剂
- `result`: 平衡化学方程式
- `createdAt`: 实验创建时间（ISO 8601格式）

**错误响应**:

用户ID缺失 (400):
```json
{
  "error": "User ID is required"
}
```

**示例请求**:
```bash
curl "http://localhost:8080/api/experiments?userId=1"
```

### 5. 获取实验统计

获取指定用户的实验统计信息。

**接口地址**: `GET /api/experiments/stats`

**查询参数**:
- `userId` (必填): 用户ID

**成功响应** (200):
```json
{
  "userId": 1,
  "totalExperiments": 5
}
```

**响应字段说明**:
- `userId`: 用户ID
- `totalExperiments`: 用户总实验次数

**错误响应**:

用户ID缺失 (400):
```json
{
  "error": "User ID is required"
}
```

**示例请求**:
```bash
curl "http://localhost:8080/api/experiments/stats?userId=1"
```

## 静态资源接口

### 6. 前端页面访问

系统提供静态HTML页面访问。

**登录页面**: `GET /login.html`
**实验界面**: `GET /experiment.html`

**响应**: HTML页面内容

**示例请求**:
```bash
curl http://localhost:8080/login.html
curl http://localhost:8080/experiment.html
```

## 监控接口

### 7. 健康检查

检查应用程序健康状态（需要启用Actuator）。

**接口地址**: `GET /actuator/health`

**成功响应** (200):
```json
{
  "status": "UP",
  "components": {
    "db": {
      "status": "UP",
      "details": {
        "database": "H2",
        "validationQuery": "isValid()"
      }
    },
    "diskSpace": {
      "status": "UP",
      "details": {
        "total": 499963174912,
        "free": 91943821312,
        "threshold": 10485760,
        "exists": true
      }
    }
  }
}
```

## 错误处理

### 常见错误场景

#### 1. 参数验证错误
```json
{
  "error": "Username must be between 3 and 50 characters"
}
```

#### 2. 业务逻辑错误
```json
{
  "error": "User already exists"
}
```

#### 3. 系统错误
```json
{
  "error": "Internal server error"
}
```

### 错误码对照表

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| Username is required | 用户名为空 | 提供有效用户名 |
| Password is required | 密码为空 | 提供有效密码 |
| Password must be at least 6 characters | 密码太短 | 使用至少6个字符的密码 |
| User already exists | 用户名已被注册 | 使用不同的用户名 |
| Invalid credentials | 登录凭据错误 | 检查用户名和密码 |
| User ID is required | 缺少用户ID参数 | 在查询参数中提供userId |
| Input reactants are required | 实验输入为空 | 提供有效的化学试剂输入 |

## 使用示例

### 完整用户流程示例

#### 1. 注册用户
```bash
curl -X POST http://localhost:8080/api/users/register \
  -H "Content-Type: application/json" \
  -d '{"username": "chemist01", "password": "secure123"}'
```

响应:
```json
{"message": "Registration successful", "userId": 1}
```

#### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/users/login \
  -H "Content-Type: application/json" \
  -d '{"username": "chemist01", "password": "secure123"}'
```

响应:
```json
{"message": "Login successful", "userId": 1}
```

#### 3. 运行实验
```bash
curl -X POST "http://localhost:8080/api/experiments/run?userId=1" \
  -H "Content-Type: application/json" \
  -d '{"input": "H2 + O2"}'
```

响应:
```json
{
  "result": "2H2 + O2 -> 2H2O",
  "warning": "High temperature may cause an explosion"
}
```

#### 4. 查看实验历史
```bash
curl "http://localhost:8080/api/experiments?userId=1"
```

响应:
```json
[
  {
    "id": 1,
    "userId": 1,
    "inputData": "H2 + O2",
    "result": "2H2 + O2 -> 2H2O",
    "createdAt": "2025-08-25T19:31:50.649303"
  }
]
```

#### 5. 查看统计信息
```bash
curl "http://localhost:8080/api/experiments/stats?userId=1"
```

响应:
```json
{
  "userId": 1,
  "totalExperiments": 1
}
```

## 开发工具

### Postman集合

可以导入以下Postman集合进行API测试：

```json
{
  "info": {
    "name": "Virtual Laboratory API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "item": [
    {
      "name": "User Registration",
      "request": {
        "method": "POST",
        "header": [{"key": "Content-Type", "value": "application/json"}],
        "body": {
          "mode": "raw",
          "raw": "{\"username\": \"testuser\", \"password\": \"password123\"}"
        },
        "url": {
          "raw": "{{baseUrl}}/api/users/register",
          "host": ["{{baseUrl}}"],
          "path": ["api", "users", "register"]
        }
      }
    }
  ],
  "variable": [
    {
      "key": "baseUrl",
      "value": "http://localhost:8080"
    }
  ]
}
```

### 测试脚本

#### 自动化测试脚本
```bash
#!/bin/bash

BASE_URL="http://localhost:8080"
USERNAME="testuser_$(date +%s)"
PASSWORD="password123"

echo "Testing Virtual Laboratory API..."

# 1. 用户注册
echo "1. Testing user registration..."
REGISTER_RESPONSE=$(curl -s -X POST "$BASE_URL/api/users/register" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")

USER_ID=$(echo $REGISTER_RESPONSE | grep -o '"userId":[0-9]*' | cut -d':' -f2)
echo "User registered with ID: $USER_ID"

# 2. 用户登录
echo "2. Testing user login..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/users/login" \
  -H "Content-Type: application/json" \
  -d "{\"username\": \"$USERNAME\", \"password\": \"$PASSWORD\"}")
echo "Login response: $LOGIN_RESPONSE"

# 3. 运行实验
echo "3. Testing experiment execution..."
EXPERIMENT_RESPONSE=$(curl -s -X POST "$BASE_URL/api/experiments/run?userId=$USER_ID" \
  -H "Content-Type: application/json" \
  -d '{"input": "H2 + O2"}')
echo "Experiment response: $EXPERIMENT_RESPONSE"

# 4. 查看实验历史
echo "4. Testing experiment history..."
HISTORY_RESPONSE=$(curl -s "$BASE_URL/api/experiments?userId=$USER_ID")
echo "History response: $HISTORY_RESPONSE"

# 5. 查看统计信息
echo "5. Testing experiment stats..."
STATS_RESPONSE=$(curl -s "$BASE_URL/api/experiments/stats?userId=$USER_ID")
echo "Stats response: $STATS_RESPONSE"

echo "API testing completed!"
```

## 版本更新

### v1.0.0 (当前版本)
- 基础用户管理功能
- 化学实验模拟功能
- 实验历史记录功能
- H2数据库支持

### 计划功能 (v1.1.0)
- 用户权限管理
- 实验模板功能
- 批量实验处理
- 实验结果导出

---

本API文档将随着系统功能的更新而持续维护。如有疑问或建议，请联系开发团队。
