# 虚拟实验室系统 (Virtual Laboratory System)

## 项目简介

虚拟实验室系统是一个基于Spring Boot的轻量级Web应用，提供化学反应模拟功能。用户可以注册账号、登录系统、进行化学实验模拟，并查看实验历史记录。

## 技术栈

### 后端技术
- **Spring Boot 2.7.14** - 主框架
- **Spring Data JPA** - 数据持久化
- **H2 Database** - 内存数据库
- **Spring Web** - REST API
- **Maven** - 依赖管理

### 前端技术
- **HTML5** - 页面结构
- **CSS3** - 样式设计
- **JavaScript (ES6)** - 交互逻辑
- **Bootstrap 5.1.3** - UI框架

## 功能特性

### 🔐 用户管理
- 用户注册（用户名唯一性验证）
- 用户登录（身份验证）
- 密码安全验证

### 🧪 化学实验模拟
- 支持多种化学反应
- 自动平衡化学方程式
- 安全警告提示
- 实时反应结果显示

### 📊 实验记录管理
- 实验历史记录保存
- 实验统计信息
- 按时间排序查看

### 🎨 用户界面
- 响应式设计
- 现代化UI界面
- 实时交互反馈

## 支持的化学反应

| 输入格式 | 平衡方程式 | 安全警告 |
|---------|-----------|---------|
| H2 + O2 | 2H2 + O2 → 2H2O | 高温可能引起爆炸 |
| Na + Cl2 | 2Na + Cl2 → 2NaCl | 剧烈反应，使用防护设备 |
| C + O2 | C + O2 → CO2 | 燃烧反应，确保通风 |
| CH4 + O2 | CH4 + 2O2 → CO2 + 2H2O | 可燃气体，有爆炸风险 |
| HCl + NaOH | HCl + NaOH → NaCl + H2O | 酸碱中和，产生热量 |
| Fe + O2 | 4Fe + 3O2 → 2Fe2O3 | 氧化反应，室温缓慢 |
| Mg + O2 | 2Mg + O2 → 2MgO | 强烈白光，勿直视 |
| NH3 + HCl | NH3 + HCl → NH4Cl | 产生白烟，确保通风 |

## 快速开始

### 环境要求
- Java 11 或更高版本
- Maven 3.6 或更高版本
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd laboratory
```

2. **编译项目**
```bash
mvn clean compile
```

3. **启动应用**
```bash
mvn spring-boot:run
```

4. **访问应用**
- 应用地址：http://localhost:8080
- 登录页面：http://localhost:8080/login.html
- 实验界面：http://localhost:8080/experiment.html
- H2控制台：http://localhost:8080/h2-console

### 数据库配置

应用使用H2内存数据库，配置信息：
- **URL**: `jdbc:h2:mem:laboratory`
- **用户名**: `sa`
- **密码**: （空）
- **驱动**: `org.h2.Driver`

## 使用指南

### 1. 用户注册
1. 访问 http://localhost:8080/login.html
2. 在注册表单中输入用户名和密码
3. 用户名要求：3-50个字符，支持字母、数字、下划线、中文
4. 密码要求：至少6个字符
5. 点击"注册"按钮

### 2. 用户登录
1. 在登录表单中输入已注册的用户名和密码
2. 点击"登录"按钮
3. 登录成功后自动跳转到实验界面

### 3. 进行化学实验
1. 在实验界面的输入框中输入化学试剂
2. 支持的格式：`H2 + O2`、`Na+Cl2`等
3. 点击"运行实验"按钮
4. 查看平衡方程式和安全警告
5. 实验结果自动保存到历史记录

### 4. 查看实验历史
- 右侧面板显示实验历史记录
- 按时间倒序排列（最新的在前）
- 显示输入试剂、结果方程式和实验时间
- 点击"刷新"按钮更新记录

### 5. 查看统计信息
- 实验历史面板下方显示总实验次数
- 实时更新统计数据

## API 接口文档

### 用户相关接口

#### 用户注册
```http
POST /api/users/register
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "message": "Registration successful",
  "userId": 1
}
```

#### 用户登录
```http
POST /api/users/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "password123"
}
```

**响应示例：**
```json
{
  "message": "Login successful",
  "userId": 1
}
```

### 实验相关接口

#### 运行实验
```http
POST /api/experiments/run?userId=1
Content-Type: application/json

{
  "input": "H2 + O2"
}
```

**响应示例：**
```json
{
  "result": "2H2 + O2 -> 2H2O",
  "warning": "High temperature may cause an explosion"
}
```

#### 查询实验历史
```http
GET /api/experiments?userId=1
```

**响应示例：**
```json
[
  {
    "id": 1,
    "userId": 1,
    "inputData": "H2 + O2",
    "result": "2H2 + O2 -> 2H2O",
    "createdAt": "2025-08-25T19:31:50.649303"
  }
]
```

#### 实验统计
```http
GET /api/experiments/stats?userId=1
```

**响应示例：**
```json
{
  "userId": 1,
  "totalExperiments": 5
}
```

## 项目结构

```
laboratory/
├── src/
│   ├── main/
│   │   ├── java/org/example/
│   │   │   ├── LaboratoryApplication.java          # 主应用类
│   │   │   ├── controller/                         # 控制器层
│   │   │   │   ├── UserController.java            # 用户API
│   │   │   │   └── ExperimentController.java      # 实验API
│   │   │   ├── entity/                            # 实体类
│   │   │   │   ├── User.java                      # 用户实体
│   │   │   │   └── Experiment.java                # 实验实体
│   │   │   ├── repository/                        # 数据访问层
│   │   │   │   ├── UserRepository.java            # 用户数据访问
│   │   │   │   └── ExperimentRepository.java      # 实验数据访问
│   │   │   └── service/                           # 业务逻辑层
│   │   │       ├── UserService.java               # 用户业务逻辑
│   │   │       ├── ExperimentService.java         # 实验业务逻辑
│   │   │       └── ChemicalReactionSimulator.java # 化学反应模拟器
│   │   └── resources/
│   │       ├── application.properties              # 应用配置
│   │       └── static/                            # 静态资源
│   │           ├── login.html                     # 登录页面
│   │           ├── experiment.html                # 实验页面
│   │           ├── css/style.css                  # 样式文件
│   │           └── js/app.js                      # JavaScript逻辑
├── pom.xml                                        # Maven配置
└── README.md                                      # 项目说明
```

## 开发指南

### 添加新的化学反应

1. 编辑 `ChemicalReactionSimulator.java`
2. 在 `REACTION_DATABASE` 中添加新反应：

```java
REACTION_DATABASE.put("输入格式", new ReactionResult("平衡方程式", "安全警告"));
```

### 扩展API功能

1. 在相应的Controller中添加新的端点
2. 在Service层实现业务逻辑
3. 如需要，在Repository层添加数据访问方法

### 自定义前端样式

1. 编辑 `src/main/resources/static/css/style.css`
2. 修改颜色、字体、布局等样式
3. 重启应用查看效果

## 故障排除

### 常见问题

**Q: 应用启动失败**
A: 检查Java版本是否为11+，确保8080端口未被占用

**Q: 无法访问H2控制台**
A: 确认访问地址为 http://localhost:8080/h2-console

**Q: 前端页面显示异常**
A: 检查浏览器控制台错误信息，确认静态资源加载正常

**Q: API返回404错误**
A: 确认请求URL正确，检查Controller映射路径

### 日志查看

应用日志会在控制台输出，包含：
- SQL执行日志
- HTTP请求日志
- 错误信息日志

## 许可证

本项目采用 MIT 许可证。

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**享受你的虚拟实验室体验！** 🧪✨
