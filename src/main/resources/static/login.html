<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟实验室 - 登录注册</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="card mt-5">
                    <div class="card-header text-center">
                        <h3>虚拟实验室</h3>
                        <p class="text-muted">化学反应模拟系统</p>
                    </div>
                    <div class="card-body">
                        <!-- 登录表单 -->
                        <form id="loginForm">
                            <h4 class="mb-3">用户登录</h4>
                            <div class="mb-3">
                                <label for="loginUsername" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="loginUsername" required>
                            </div>
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">密码</label>
                                <input type="password" class="form-control" id="loginPassword" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">登录</button>
                        </form>

                        <hr class="my-4">

                        <!-- 注册表单 -->
                        <form id="registerForm">
                            <h4 class="mb-3">用户注册</h4>
                            <div class="mb-3">
                                <label for="registerUsername" class="form-label">用户名</label>
                                <input type="text" class="form-control" id="registerUsername" required>
                                <div class="form-text">用户名长度为3-50个字符</div>
                            </div>
                            <div class="mb-3">
                                <label for="registerPassword" class="form-label">密码</label>
                                <input type="password" class="form-control" id="registerPassword" required>
                                <div class="form-text">密码至少6个字符</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">确认密码</label>
                                <input type="password" class="form-control" id="confirmPassword" required>
                            </div>
                            <button type="submit" class="btn btn-success w-100">注册</button>
                        </form>
                    </div>
                </div>

                <!-- 消息提示区域 -->
                <div id="messageArea" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- 加载动画 -->
    <div id="loadingSpinner" class="d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 登录表单提交处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('loginUsername').value.trim();
            const password = document.getElementById('loginPassword').value;
            
            if (!username || !password) {
                showMessage('请填写完整的登录信息', 'danger');
                return;
            }
            
            showLoading(true);
            
            try {
                const response = await fetch('/api/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showMessage('登录成功！正在跳转...', 'success');
                    localStorage.setItem('userId', data.userId);
                    localStorage.setItem('username', username);
                    setTimeout(() => {
                        window.location.href = 'experiment.html';
                    }, 1000);
                } else {
                    showMessage(data.error || '登录失败', 'danger');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'danger');
            } finally {
                showLoading(false);
            }
        });
        
        // 注册表单提交处理
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('registerUsername').value.trim();
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!username || !password || !confirmPassword) {
                showMessage('请填写完整的注册信息', 'danger');
                return;
            }
            
            if (password !== confirmPassword) {
                showMessage('两次输入的密码不一致', 'danger');
                return;
            }
            
            if (username.length < 3 || username.length > 50) {
                showMessage('用户名长度必须在3-50个字符之间', 'danger');
                return;
            }
            
            if (password.length < 6) {
                showMessage('密码至少需要6个字符', 'danger');
                return;
            }
            
            showLoading(true);
            
            try {
                const response = await fetch('/api/users/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showMessage('注册成功！请使用新账号登录', 'success');
                    document.getElementById('registerForm').reset();
                    document.getElementById('loginUsername').value = username;
                } else {
                    showMessage(data.error || '注册失败', 'danger');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'danger');
            } finally {
                showLoading(false);
            }
        });
    </script>
</body>
</html>
