<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>虚拟实验室 - 实验界面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">虚拟实验室</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">欢迎, <span id="usernameDisplay"></span></span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">退出登录</button>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <!-- 实验操作区域 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>化学反应实验</h4>
                    </div>
                    <div class="card-body">
                        <form id="experimentForm">
                            <div class="mb-3">
                                <label for="reactants" class="form-label">输入化学试剂</label>
                                <input type="text" class="form-control" id="reactants" 
                                       placeholder="例如: H2 + O2" required>
                                <div class="form-text">
                                    支持的反应: H2+O2, Na+Cl2, C+O2, CH4+O2, HCl+NaOH, Fe+O2, Mg+O2, NH3+HCl 等
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <span id="runButtonText">运行实验</span>
                                <span id="runButtonSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                            </button>
                        </form>

                        <!-- 实验结果展示区域 -->
                        <div id="experimentResult" class="mt-4 d-none">
                            <h5>实验结果</h5>
                            <div class="alert alert-info">
                                <strong>平衡方程式:</strong>
                                <div id="balancedEquation" class="mt-2"></div>
                            </div>
                            <div class="alert alert-warning">
                                <strong>安全警告:</strong>
                                <div id="safetyWarning" class="mt-2"></div>
                            </div>
                        </div>

                        <!-- 消息提示区域 -->
                        <div id="messageArea" class="mt-3"></div>
                    </div>
                </div>
            </div>

            <!-- 历史记录区域 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">实验历史</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadExperimentHistory()">
                            刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="experimentHistory">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">加载历史记录...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6 class="mb-0">实验统计</h6>
                    </div>
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col">
                                <h4 id="totalExperiments" class="text-primary">0</h4>
                                <small class="text-muted">总实验次数</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/app.js"></script>
    <script>
        // 页面加载时检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            loadExperimentHistory();
            loadExperimentStats();
        });

        // 检查登录状态
        function checkLoginStatus() {
            const userId = localStorage.getItem('userId');
            const username = localStorage.getItem('username');
            
            if (!userId || !username) {
                window.location.href = 'login.html';
                return;
            }
            
            document.getElementById('usernameDisplay').textContent = username;
        }

        // 实验表单提交处理
        document.getElementById('experimentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const reactants = document.getElementById('reactants').value.trim();
            const userId = localStorage.getItem('userId');
            
            if (!reactants) {
                showMessage('请输入化学试剂', 'danger');
                return;
            }
            
            setRunButtonLoading(true);
            
            try {
                const response = await fetch(`/api/experiments/run?userId=${userId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ input: reactants })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showExperimentResult(data.result, data.warning);
                    document.getElementById('reactants').value = '';
                    loadExperimentHistory();
                    loadExperimentStats();
                } else {
                    showMessage(data.error || '实验执行失败', 'danger');
                }
            } catch (error) {
                showMessage('网络错误，请稍后重试', 'danger');
            } finally {
                setRunButtonLoading(false);
            }
        });

        // 显示实验结果
        function showExperimentResult(result, warning) {
            document.getElementById('balancedEquation').textContent = result;
            document.getElementById('safetyWarning').textContent = warning;
            document.getElementById('experimentResult').classList.remove('d-none');
        }

        // 设置运行按钮加载状态
        function setRunButtonLoading(loading) {
            const button = document.querySelector('#experimentForm button[type="submit"]');
            const buttonText = document.getElementById('runButtonText');
            const buttonSpinner = document.getElementById('runButtonSpinner');
            
            if (loading) {
                button.disabled = true;
                buttonText.textContent = '运行中...';
                buttonSpinner.classList.remove('d-none');
            } else {
                button.disabled = false;
                buttonText.textContent = '运行实验';
                buttonSpinner.classList.add('d-none');
            }
        }

        // 加载实验历史
        async function loadExperimentHistory() {
            const userId = localStorage.getItem('userId');
            const historyContainer = document.getElementById('experimentHistory');
            
            try {
                const response = await fetch(`/api/experiments?userId=${userId}`);
                const experiments = await response.json();
                
                if (response.ok) {
                    if (experiments.length === 0) {
                        historyContainer.innerHTML = '<p class="text-muted text-center">暂无实验记录</p>';
                    } else {
                        historyContainer.innerHTML = experiments.map(exp => `
                            <div class="border-bottom pb-2 mb-2">
                                <small class="text-muted">${formatDate(exp.createdAt)}</small>
                                <div><strong>输入:</strong> ${exp.inputData}</div>
                                <div><strong>结果:</strong> ${exp.result}</div>
                            </div>
                        `).join('');
                    }
                } else {
                    historyContainer.innerHTML = '<p class="text-danger text-center">加载失败</p>';
                }
            } catch (error) {
                historyContainer.innerHTML = '<p class="text-danger text-center">网络错误</p>';
            }
        }

        // 加载实验统计
        async function loadExperimentStats() {
            const userId = localStorage.getItem('userId');
            
            try {
                const response = await fetch(`/api/experiments/stats?userId=${userId}`);
                const stats = await response.json();
                
                if (response.ok) {
                    document.getElementById('totalExperiments').textContent = stats.totalExperiments;
                }
            } catch (error) {
                console.error('Failed to load experiment stats:', error);
            }
        }

        // 退出登录
        function logout() {
            localStorage.removeItem('userId');
            localStorage.removeItem('username');
            window.location.href = 'login.html';
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
    </script>
</body>
</html>
