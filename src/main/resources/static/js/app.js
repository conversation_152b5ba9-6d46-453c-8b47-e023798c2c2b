/**
 * Virtual Laboratory JavaScript Application
 * 
 * This file contains common JavaScript functions and utilities
 * used across the virtual laboratory application.
 */

// API 基础配置
const API_BASE_URL = '';

// 通用工具函数

/**
 * 显示消息提示
 * @param {string} message - 要显示的消息
 * @param {string} type - 消息类型 (success, danger, warning, info)
 * @param {number} duration - 显示持续时间（毫秒），默认5000ms
 */
function showMessage(message, type = 'info', duration = 5000) {
    const messageArea = document.getElementById('messageArea');
    if (!messageArea) return;

    // 创建消息元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 清除之前的消息
    messageArea.innerHTML = '';
    messageArea.appendChild(alertDiv);

    // 自动隐藏消息
    if (duration > 0) {
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    }
}

/**
 * 显示/隐藏加载动画
 * @param {boolean} show - 是否显示加载动画
 */
function showLoading(show) {
    const spinner = document.getElementById('loadingSpinner');
    if (spinner) {
        if (show) {
            spinner.classList.remove('d-none');
        } else {
            spinner.classList.add('d-none');
        }
    }
}

/**
 * 格式化日期时间
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(dateString) {
    const date = new Date(dateString);
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };
    return date.toLocaleString('zh-CN', options);
}

/**
 * 验证用户名格式
 * @param {string} username - 用户名
 * @returns {object} 验证结果 {valid: boolean, message: string}
 */
function validateUsername(username) {
    if (!username || username.trim().length === 0) {
        return { valid: false, message: '用户名不能为空' };
    }
    
    const trimmed = username.trim();
    if (trimmed.length < 3) {
        return { valid: false, message: '用户名至少需要3个字符' };
    }
    
    if (trimmed.length > 50) {
        return { valid: false, message: '用户名不能超过50个字符' };
    }
    
    // 检查是否包含特殊字符（只允许字母、数字、下划线）
    const validPattern = /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/;
    if (!validPattern.test(trimmed)) {
        return { valid: false, message: '用户名只能包含字母、数字、下划线和中文字符' };
    }
    
    return { valid: true, message: '' };
}

/**
 * 验证密码格式
 * @param {string} password - 密码
 * @returns {object} 验证结果 {valid: boolean, message: string}
 */
function validatePassword(password) {
    if (!password) {
        return { valid: false, message: '密码不能为空' };
    }
    
    if (password.length < 6) {
        return { valid: false, message: '密码至少需要6个字符' };
    }
    
    if (password.length > 100) {
        return { valid: false, message: '密码不能超过100个字符' };
    }
    
    return { valid: true, message: '' };
}

/**
 * 验证化学反应输入格式
 * @param {string} input - 化学反应输入
 * @returns {object} 验证结果 {valid: boolean, message: string}
 */
function validateChemicalInput(input) {
    if (!input || input.trim().length === 0) {
        return { valid: false, message: '请输入化学试剂' };
    }
    
    const trimmed = input.trim();
    if (trimmed.length > 255) {
        return { valid: false, message: '输入内容过长' };
    }
    
    // 基本的化学式格式检查（允许字母、数字、+、-、空格、括号）
    const validPattern = /^[a-zA-Z0-9+\-\s()]+$/;
    if (!validPattern.test(trimmed)) {
        return { valid: false, message: '输入格式不正确，请使用标准化学式格式' };
    }
    
    return { valid: true, message: '' };
}

/**
 * 安全的 localStorage 操作
 */
const Storage = {
    /**
     * 设置 localStorage 项目
     * @param {string} key - 键名
     * @param {any} value - 值
     */
    set(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Failed to set localStorage item:', error);
        }
    },
    
    /**
     * 获取 localStorage 项目
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {any} 存储的值或默认值
     */
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('Failed to get localStorage item:', error);
            return defaultValue;
        }
    },
    
    /**
     * 删除 localStorage 项目
     * @param {string} key - 键名
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Failed to remove localStorage item:', error);
        }
    },
    
    /**
     * 清空 localStorage
     */
    clear() {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
        }
    }
};

/**
 * HTTP 请求工具
 */
const Http = {
    /**
     * 发送 GET 请求
     * @param {string} url - 请求URL
     * @returns {Promise} 请求Promise
     */
    async get(url) {
        try {
            const response = await fetch(url);
            return await this.handleResponse(response);
        } catch (error) {
            throw new Error('网络请求失败');
        }
    },
    
    /**
     * 发送 POST 请求
     * @param {string} url - 请求URL
     * @param {object} data - 请求数据
     * @returns {Promise} 请求Promise
     */
    async post(url, data) {
        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
            return await this.handleResponse(response);
        } catch (error) {
            throw new Error('网络请求失败');
        }
    },
    
    /**
     * 处理响应
     * @param {Response} response - Fetch响应对象
     * @returns {Promise} 处理后的响应数据
     */
    async handleResponse(response) {
        const data = await response.json();
        
        if (response.ok) {
            return { success: true, data };
        } else {
            return { success: false, error: data.error || '请求失败' };
        }
    }
};

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// 全局错误处理
window.addEventListener('error', function(event) {
    console.error('Global error:', event.error);
    showMessage('发生了未知错误，请刷新页面重试', 'danger');
});

// 全局未处理的 Promise 拒绝处理
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    showMessage('网络请求失败，请检查网络连接', 'danger');
    event.preventDefault();
});
