/* Virtual Laboratory Custom Styles */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #1e7e34, #155724);
}

/* 表单样式增强 */
.form-control {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 消息提示样式 */
.alert {
    border-radius: 6px;
    border: none;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    color: #856404;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.5rem;
}

/* 加载动画样式 */
#loadingSpinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
}

/* 实验历史记录样式 */
#experimentHistory {
    max-height: 400px;
    overflow-y: auto;
}

#experimentHistory::-webkit-scrollbar {
    width: 6px;
}

#experimentHistory::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#experimentHistory::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#experimentHistory::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 实验结果样式 */
#experimentResult {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 统计数字样式 */
#totalExperiments {
    font-weight: 700;
    font-size: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    #experimentHistory {
        max-height: 300px;
    }
}

/* 登录页面特殊样式 */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 实验输入框特殊样式 */
#reactants {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
}

/* 化学方程式显示样式 */
#balancedEquation {
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
    color: #0056b3;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

/* 安全警告样式 */
#safetyWarning {
    font-weight: 500;
    color: #856404;
}

/* 历史记录项目样式 */
.border-bottom {
    border-color: #dee2e6 !important;
}

/* 工具提示样式 */
.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 加载状态样式 */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* 页面标题样式 */
h3, h4, h5 {
    color: #343a40;
    font-weight: 600;
}

/* 链接样式 */
a {
    text-decoration: none;
    transition: color 0.2s ease-in-out;
}

a:hover {
    text-decoration: none;
}
