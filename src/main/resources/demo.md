明白了！以下是一个完整的技术方案文档，包括前端页面设计、后端接口定义、以及数据库的设计，适用于一个整体的虚拟实验室轻量化项目。

---

# **简化版虚拟实验室技术方案**

---

## **项目背景与目标**

### 项目背景
本项目旨在开发一个轻量化基于 Web 的虚拟实验室系统，允许用户模拟化学反应、查看实验结果并保存实验记录。通过简单的前端页面与 RESTful 接口的交互，用户可以完成注册、登录、实验操作，并从系统中获取化学反应的模拟反馈，同时支持实验记录的保存和查询。

### 项目目标
1. 提供用户友好的前端页面，完成用户认证及实验交互。
2. 实现一个简单后端 API 服务，满足基本实验模拟需求。
3. 通过轻量级数据库 (H2) 存储用户数据与实验记录。

---

## **系统功能描述**

### 功能说明

#### 1. 用户模块
- 用户注册
- 用户登录（通过用户名和密码）
- 用户身份验证，确保操作权限

#### 2. 实验模块
- 输入化学试剂进行反应模拟
- 返回化学反应的平衡方程式
- 提供实验警告（通过静态规则库检测危险性）

#### 3. 实验记录模块
- 实验完成后自动存储用户输入的数据和结果
- 支持按用户查询历史实验记录（按时间排序）

---

## **系统架构设计**

### 系统架构图

```
+------------------------------------+
|             前端页面               |
| HTML + CSS + JavaScript            |
+------------------------------------+
                 |
                 ↓
+------------------------------------+
|          后端 RESTful API          |
| Spring Boot                        |
+------------------------------------+
| 用户模块 | 实验模块 | 实验记录模块 |
+------------------------------------+
                 ↓
+------------------------------------+
|          数据存储 H2 数据库         |
| 用户表 | 实验记录表                 |
+------------------------------------+
```

### 技术选型
| 层        | 技术栈                     | 理由                          |
|-----------|----------------------------|-------------------------------|
| 前端      | HTML5, CSS, JavaScript     | 快速开发，易部署              |
| 前端库    | Vanilla JS、Bootstrap      | 简化式 UI 框架，无需复杂性    |
| 后端      | Spring Boot, Spring MVC    | 快速构建轻量级 REST API 服务  |
| 数据库    | H2 (内存数据库)            | 开发方便，无需复杂配置        |
| 依赖管理  | Maven                      | 管理项目依赖                 |

---

## **数据库设计**

### 1. 用户表 (users)
| 字段名称    | 类型         | 描述                          |
|-------------|--------------|-------------------------------|
| id          | INT          | 用户唯一标识 (主键，自增)      |
| username    | VARCHAR(50)  | 用户名，唯一                 |
| password    | VARCHAR(255) | 加密后的密码                  |

### 2. 实验记录表 (experiments)
| 字段名称    | 类型         | 描述                          |
|-------------|--------------|-------------------------------|
| id          | INT          | 实验记录唯一标识 (主键，自增) |
| user_id     | INT          | 与用户表关联的外键            |
| input_data  | VARCHAR(255) | 用户输入的化学试剂            |
| result      | VARCHAR(255) | 实验返回的平衡反应方程式      |
| created_at  | TIMESTAMP    | 实验创建时间                  |

---

## **前端页面设计**

### 页面 1：用户注册和登录页
- **功能**：提供注册与登录入口。
- **页面大纲**：
  ```
  +----------------------------------+
  |               用户登录            |
  +----------------------------------+
  | 用户名输入框                     |
  | 密码输入框                       |
  | [登录按钮] [注册按钮]            |
  +----------------------------------+
  ```

### 页面 2：实验界面
- **功能**：提供化学反应输入，返回实验结果并展示历史实验记录。
- **页面大纲**：
  ```
  +----------------------------------+
  |             实验页面             |
  +----------------------------------+
  | 输入试剂框 (如：H2 + O2)          |
  | [运行实验按钮]                    |
  +----------------------------------+
  | 实验结果展示                     |
  | - 平衡方程式                     |
  | - 实验警告                       |
  +----------------------------------+
  | 历史记录：                       |
  | - 实验1 数据                     |
  | - 实验2 数据                     |
  | ...                              |
  +----------------------------------+
  ```

### 样式说明
- **主色调**：采用简约风格，白色背景搭配蓝色按钮。
- **前端交互**：使用 JavaScript 实现按钮点击和结果动态加载。

---

## **后端接口定义**

### RESTful API 设计

#### 用户模块

1. **用户注册**
    - 方法：`POST`
    - 地址：`/api/users/register`
    - 请求体：
      ```json
      {
        "username": "testuser",
        "password": "password123"
      }
      ```
    - 响应：
        - 成功：`{ "message": "Registration successful" }`
        - 用户已存在：`{ "error": "User already exists" }`

2. **用户登录**
    - 方法：`POST`
    - 地址：`/api/users/login`
    - 请求体：
      ```json
      {
        "username": "testuser",
        "password": "password123"
      }
      ```
    - 响应：
        - 成功：`{ "message": "Login successful", "userId": 1 }`
        - 失败：`{ "error": "Invalid credentials" }`

---

#### 实验模块

1. **运行实验**
    - 方法：`POST`
    - 地址：`/api/experiments/run`
    - 查询参数：`userId`
    - 请求体：
      ```json
      {
        "input": "H2 + O2"
      }
      ```
    - 响应：
      ```json
      {
        "result": "2H2 + O2 -> 2H2O",
        "warning": "High temperature may cause an explosion"
      }
      ```

---

#### 实验记录模块

1. **查询实验记录**
    - 方法：`GET`
    - 地址：`/api/experiments`
    - 查询参数：`userId`
    - 响应：
      ```json
      [
        {
          "id": 1,
          "userId": 1,
          "input_data": "H2 + O2",
          "result": "2H2 + O2 -> 2H2O",
          "created_at": "2023-10-01T12:00:00"
        }
      ]
      ```
