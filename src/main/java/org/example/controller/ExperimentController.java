package org.example.controller;

import org.example.entity.Experiment;
import org.example.service.ExperimentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Experiment Controller
 * 
 * REST API controller for experiment-related operations including
 * running experiments and retrieving experiment history.
 */
@RestController
@RequestMapping("/api/experiments")
@CrossOrigin(origins = "*")
public class ExperimentController {

    @Autowired
    private ExperimentService experimentService;

    /**
     * Run experiment endpoint
     * 
     * @param userId the user ID (query parameter)
     * @param request containing input reactants
     * @return experiment result with balanced equation and warning
     */
    @PostMapping("/run")
    public ResponseEntity<Map<String, Object>> runExperiment(
            @RequestParam Long userId,
            @RequestBody ExperimentRequest request) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input
            if (userId == null) {
                response.put("error", "User ID is required");
                return ResponseEntity.badRequest().body(response);
            }
            
            if (request.getInput() == null || request.getInput().trim().isEmpty()) {
                response.put("error", "Input reactants are required");
                return ResponseEntity.badRequest().body(response);
            }

            // Run experiment
            ExperimentService.ExperimentResult result = experimentService.runExperiment(userId, request.getInput());
            
            response.put("result", result.getResult());
            response.put("warning", result.getWarning());
            return ResponseEntity.ok(response);
            
        } catch (RuntimeException e) {
            response.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(response);
        } catch (Exception e) {
            response.put("error", "Experiment execution failed");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get experiment history endpoint
     * 
     * @param userId the user ID (query parameter)
     * @return list of user's experiment history
     */
    @GetMapping
    public ResponseEntity<List<Experiment>> getExperimentHistory(@RequestParam Long userId) {
        try {
            // Validate input
            if (userId == null) {
                return ResponseEntity.badRequest().build();
            }

            // Get experiment history
            List<Experiment> experiments = experimentService.getExperimentHistory(userId);
            return ResponseEntity.ok(experiments);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get experiment statistics endpoint
     * 
     * @param userId the user ID (query parameter)
     * @return experiment statistics for the user
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getExperimentStats(@RequestParam Long userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // Validate input
            if (userId == null) {
                response.put("error", "User ID is required");
                return ResponseEntity.badRequest().body(response);
            }

            // Get experiment statistics
            long experimentCount = experimentService.getExperimentCount(userId);
            
            response.put("userId", userId);
            response.put("totalExperiments", experimentCount);
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            response.put("error", "Failed to retrieve statistics");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Inner class for experiment request data
     */
    public static class ExperimentRequest {
        private String input;

        public ExperimentRequest() {}

        public ExperimentRequest(String input) {
            this.input = input;
        }

        public String getInput() {
            return input;
        }

        public void setInput(String input) {
            this.input = input;
        }
    }
}
