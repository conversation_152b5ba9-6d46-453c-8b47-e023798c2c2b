package org.example.entity;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * Experiment Entity Class
 * 
 * Represents an experiment record in the virtual laboratory system.
 * Maps to the 'experiments' table in the database.
 */
@Entity
@Table(name = "experiments")
public class Experiment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "input_data", nullable = false, length = 255)
    @NotBlank(message = "Input data is required")
    private String inputData;

    @Column(nullable = false, length = 255)
    @NotBlank(message = "Result is required")
    private String result;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    // Default constructor
    public Experiment() {
        this.createdAt = LocalDateTime.now();
    }

    // Constructor with parameters
    public Experiment(Long userId, String inputData, String result) {
        this.userId = userId;
        this.inputData = inputData;
        this.result = result;
        this.createdAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getInputData() {
        return inputData;
    }

    public void setInputData(String inputData) {
        this.inputData = inputData;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "Experiment{" +
                "id=" + id +
                ", userId=" + userId +
                ", inputData='" + inputData + '\'' +
                ", result='" + result + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Experiment experiment = (Experiment) o;
        return id != null && id.equals(experiment.id);
    }

    @Override
    public int hashCode() {
        return getClass().hashCode();
    }
}
