package org.example.repository;

import org.example.entity.Experiment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Experiment Repository Interface
 * 
 * Provides data access operations for Experiment entities.
 * Extends JpaRepository to inherit basic CRUD operations.
 */
@Repository
public interface ExperimentRepository extends JpaRepository<Experiment, Long> {

    /**
     * Find all experiments by user ID, ordered by creation time descending
     * 
     * @param userId the user ID to search for
     * @return List of experiments for the specified user
     */
    List<Experiment> findByUserIdOrderByCreatedAtDesc(Long userId);

    /**
     * Count the number of experiments for a specific user
     * 
     * @param userId the user ID to count experiments for
     * @return the number of experiments for the user
     */
    long countByUserId(Long userId);
}
