package org.example.service;

import org.example.entity.User;
import org.example.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * User Service Class
 * 
 * Provides business logic for user-related operations including
 * registration, login, and user management.
 */
@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    /**
     * Register a new user
     * 
     * @param username the username for the new user
     * @param password the password for the new user
     * @return the registered user
     * @throws RuntimeException if username already exists
     */
    public User registerUser(String username, String password) {
        // Check if username already exists
        if (userRepository.existsByUsername(username)) {
            throw new RuntimeException("User already exists");
        }

        // Create new user (in a real application, password should be encrypted)
        User user = new User(username, password);
        return userRepository.save(user);
    }

    /**
     * Authenticate user login
     * 
     * @param username the username
     * @param password the password
     * @return the authenticated user
     * @throws RuntimeException if credentials are invalid
     */
    public User loginUser(String username, String password) {
        Optional<User> userOptional = userRepository.findByUsername(username);
        
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            // In a real application, password should be encrypted and compared securely
            if (user.getPassword().equals(password)) {
                return user;
            }
        }
        
        throw new RuntimeException("Invalid credentials");
    }

    /**
     * Find user by ID
     * 
     * @param userId the user ID
     * @return Optional containing the user if found
     */
    public Optional<User> findById(Long userId) {
        return userRepository.findById(userId);
    }

    /**
     * Find user by username
     * 
     * @param username the username
     * @return Optional containing the user if found
     */
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * Check if username exists
     * 
     * @param username the username to check
     * @return true if username exists, false otherwise
     */
    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }
}
