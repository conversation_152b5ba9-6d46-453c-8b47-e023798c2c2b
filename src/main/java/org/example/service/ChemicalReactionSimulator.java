package org.example.service;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Chemical Reaction Simulator
 * 
 * Simulates chemical reactions and provides balanced equations
 * and safety warnings based on input reactants.
 */
@Component
public class ChemicalReactionSimulator {

    // Static reaction database for simulation
    private static final Map<String, ReactionResult> REACTION_DATABASE = new HashMap<>();

    static {
        // Initialize common chemical reactions
        REACTION_DATABASE.put("H2 + O2", new ReactionResult("2H2 + O2 -> 2H2O", "High temperature may cause an explosion"));
        REACTION_DATABASE.put("H2+O2", new ReactionResult("2H2 + O2 -> 2H2O", "High temperature may cause an explosion"));
        REACTION_DATABASE.put("Na + Cl2", new ReactionResult("2Na + Cl2 -> 2NaCl", "Violent reaction, use protective equipment"));
        REACTION_DATABASE.put("Na+Cl2", new ReactionResult("2Na + Cl2 -> 2NaCl", "Violent reaction, use protective equipment"));
        REACTION_DATABASE.put("C + O2", new ReactionResult("C + O2 -> CO2", "Combustion reaction, ensure proper ventilation"));
        REACTION_DATABASE.put("C+O2", new ReactionResult("C + O2 -> CO2", "Combustion reaction, ensure proper ventilation"));
        REACTION_DATABASE.put("CH4 + O2", new ReactionResult("CH4 + 2O2 -> CO2 + 2H2O", "Combustible gas, risk of explosion"));
        REACTION_DATABASE.put("CH4+O2", new ReactionResult("CH4 + 2O2 -> CO2 + 2H2O", "Combustible gas, risk of explosion"));
        REACTION_DATABASE.put("HCl + NaOH", new ReactionResult("HCl + NaOH -> NaCl + H2O", "Acid-base neutralization, generates heat"));
        REACTION_DATABASE.put("HCl+NaOH", new ReactionResult("HCl + NaOH -> NaCl + H2O", "Acid-base neutralization, generates heat"));
        REACTION_DATABASE.put("Fe + O2", new ReactionResult("4Fe + 3O2 -> 2Fe2O3", "Oxidation reaction, slow at room temperature"));
        REACTION_DATABASE.put("Fe+O2", new ReactionResult("4Fe + 3O2 -> 2Fe2O3", "Oxidation reaction, slow at room temperature"));
        REACTION_DATABASE.put("Mg + O2", new ReactionResult("2Mg + O2 -> 2MgO", "Bright white light, do not look directly"));
        REACTION_DATABASE.put("Mg+O2", new ReactionResult("2Mg + O2 -> 2MgO", "Bright white light, do not look directly"));
        REACTION_DATABASE.put("CaCO3", new ReactionResult("CaCO3 -> CaO + CO2", "Thermal decomposition, requires high temperature"));
        REACTION_DATABASE.put("NH3 + HCl", new ReactionResult("NH3 + HCl -> NH4Cl", "Forms white smoke, ensure ventilation"));
        REACTION_DATABASE.put("NH3+HCl", new ReactionResult("NH3 + HCl -> NH4Cl", "Forms white smoke, ensure ventilation"));
    }

    /**
     * Simulate a chemical reaction based on input reactants
     * 
     * @param input the input reactants (e.g., "H2 + O2")
     * @return ReactionResult containing balanced equation and warning
     */
    public ReactionResult simulateReaction(String input) {
        if (input == null || input.trim().isEmpty()) {
            return new ReactionResult("Invalid input", "Please provide valid chemical reactants");
        }

        // Normalize input (remove extra spaces, convert to uppercase for comparison)
        String normalizedInput = normalizeInput(input);

        // Check if reaction exists in database
        ReactionResult result = REACTION_DATABASE.get(normalizedInput);
        if (result != null) {
            return result;
        }

        // If reaction not found, provide a generic response
        return new ReactionResult(
            "Reaction not found in database: " + input,
            "Unknown reaction - please verify reactants and consult safety guidelines"
        );
    }

    /**
     * Normalize input string for consistent matching
     * 
     * @param input the raw input string
     * @return normalized input string
     */
    private String normalizeInput(String input) {
        return input.trim()
                   .replaceAll("\\s+", " ")  // Replace multiple spaces with single space
                   .replaceAll(" \\+ ", "+")  // Remove spaces around +
                   .replaceAll("\\+ ", "+")   // Remove space after +
                   .replaceAll(" \\+", "+");  // Remove space before +
    }

    /**
     * Inner class to represent reaction results
     */
    public static class ReactionResult {
        private final String result;
        private final String warning;

        public ReactionResult(String result, String warning) {
            this.result = result;
            this.warning = warning;
        }

        public String getResult() {
            return result;
        }

        public String getWarning() {
            return warning;
        }

        @Override
        public String toString() {
            return "ReactionResult{" +
                    "result='" + result + '\'' +
                    ", warning='" + warning + '\'' +
                    '}';
        }
    }
}
