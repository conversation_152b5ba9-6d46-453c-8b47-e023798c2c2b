package org.example.service;

import org.example.entity.Experiment;
import org.example.repository.ExperimentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Experiment Service Class
 * 
 * Provides business logic for experiment-related operations including
 * running experiments, saving results, and retrieving experiment history.
 */
@Service
public class ExperimentService {

    @Autowired
    private ExperimentRepository experimentRepository;

    @Autowired
    private ChemicalReactionSimulator reactionSimulator;

    /**
     * Run a chemical experiment simulation
     * 
     * @param userId the ID of the user running the experiment
     * @param input the chemical reactants input
     * @return the experiment result with balanced equation and warning
     */
    public ExperimentResult runExperiment(Long userId, String input) {
        // Validate input
        if (input == null || input.trim().isEmpty()) {
            throw new RuntimeException("Input cannot be empty");
        }

        // Simulate the chemical reaction
        ChemicalReactionSimulator.ReactionResult reactionResult = reactionSimulator.simulateReaction(input);

        // Save experiment record to database
        Experiment experiment = new Experiment(userId, input.trim(), reactionResult.getResult());
        experimentRepository.save(experiment);

        // Return result
        return new ExperimentResult(reactionResult.getResult(), reactionResult.getWarning());
    }

    /**
     * Get experiment history for a specific user
     * 
     * @param userId the user ID
     * @return list of experiments ordered by creation time (newest first)
     */
    public List<Experiment> getExperimentHistory(Long userId) {
        return experimentRepository.findByUserIdOrderByCreatedAtDesc(userId);
    }

    /**
     * Get experiment count for a specific user
     * 
     * @param userId the user ID
     * @return the number of experiments performed by the user
     */
    public long getExperimentCount(Long userId) {
        return experimentRepository.countByUserId(userId);
    }

    /**
     * Inner class to represent experiment execution results
     */
    public static class ExperimentResult {
        private final String result;
        private final String warning;

        public ExperimentResult(String result, String warning) {
            this.result = result;
            this.warning = warning;
        }

        public String getResult() {
            return result;
        }

        public String getWarning() {
            return warning;
        }

        @Override
        public String toString() {
            return "ExperimentResult{" +
                    "result='" + result + '\'' +
                    ", warning='" + warning + '\'' +
                    '}';
        }
    }
}
