# 虚拟实验室系统 - 部署运维指南

## 部署环境要求

### 最低系统要求
- **操作系统**: Linux/Windows/macOS
- **Java版本**: OpenJDK 11 或更高版本
- **内存**: 最少512MB，推荐1GB+
- **磁盘空间**: 最少100MB
- **网络**: 需要访问Maven中央仓库（首次构建）

### 推荐生产环境
- **CPU**: 2核心或更多
- **内存**: 2GB或更多
- **操作系统**: Ubuntu 20.04 LTS / CentOS 8
- **Java**: OpenJDK 11 LTS

## 本地开发部署

### 1. 环境准备

#### 安装Java 11
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install openjdk-11-jdk

# CentOS/RHEL
sudo yum install java-11-openjdk-devel

# macOS (使用Homebrew)
brew install openjdk@11

# 验证安装
java -version
javac -version
```

#### 安装Maven
```bash
# Ubuntu/Debian
sudo apt install maven

# CentOS/RHEL
sudo yum install maven

# macOS
brew install maven

# 验证安装
mvn -version
```

### 2. 项目构建

```bash
# 克隆项目
git clone <repository-url>
cd laboratory

# 清理并编译
mvn clean compile

# 运行测试（如果有）
mvn test

# 打包应用
mvn package
```

### 3. 启动应用

#### 开发模式启动
```bash
mvn spring-boot:run
```

#### 生产模式启动
```bash
# 使用打包后的JAR文件
java -jar target/laboratory-1.0-SNAPSHOT.jar

# 指定配置文件
java -jar target/laboratory-1.0-SNAPSHOT.jar --spring.config.location=classpath:/application-prod.properties

# 指定端口
java -jar target/laboratory-1.0-SNAPSHOT.jar --server.port=9090
```

## 生产环境部署

### 1. 使用Systemd服务（Linux）

创建服务文件：
```bash
sudo nano /etc/systemd/system/laboratory.service
```

服务配置内容：
```ini
[Unit]
Description=Virtual Laboratory Application
After=network.target

[Service]
Type=simple
User=laboratory
WorkingDirectory=/opt/laboratory
ExecStart=/usr/bin/java -jar /opt/laboratory/laboratory-1.0-SNAPSHOT.jar
Restart=always
RestartSec=10

# 环境变量
Environment=JAVA_OPTS="-Xms512m -Xmx1024m"
Environment=SPRING_PROFILES_ACTIVE=prod

# 日志配置
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start laboratory

# 设置开机自启
sudo systemctl enable laboratory

# 查看服务状态
sudo systemctl status laboratory

# 查看日志
sudo journalctl -u laboratory -f
```

### 2. 使用Docker部署

#### 创建Dockerfile
```dockerfile
FROM openjdk:11-jre-slim

# 设置工作目录
WORKDIR /app

# 复制JAR文件
COPY target/laboratory-1.0-SNAPSHOT.jar app.jar

# 暴露端口
EXPOSE 8080

# 设置JVM参数
ENV JAVA_OPTS="-Xms512m -Xmx1024m"

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

#### 构建和运行Docker镜像
```bash
# 构建镜像
docker build -t virtual-laboratory:latest .

# 运行容器
docker run -d \
  --name laboratory \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  virtual-laboratory:latest

# 查看容器状态
docker ps

# 查看日志
docker logs -f laboratory
```

#### 使用Docker Compose
创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  laboratory:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - JAVA_OPTS=-Xms512m -Xmx1024m
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
```

运行：
```bash
# 启动服务
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 配置管理

### 1. 生产环境配置

创建 `application-prod.properties`：
```properties
# 服务器配置
server.port=8080
server.servlet.context-path=/

# 数据库配置（生产环境可考虑使用MySQL/PostgreSQL）
spring.datasource.url=jdbc:h2:file:./data/laboratory
spring.datasource.username=sa
spring.datasource.password=your_secure_password

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false

# 日志配置
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=WARN
logging.file.name=logs/laboratory.log
logging.file.max-size=10MB
logging.file.max-history=30

# 应用信息
spring.application.name=Virtual Laboratory Production

# 安全配置
server.error.include-stacktrace=never
server.error.include-message=never
```

### 2. 环境变量配置

```bash
# 设置环境变量
export SPRING_PROFILES_ACTIVE=prod
export SERVER_PORT=8080
export DATABASE_URL=jdbc:h2:file:./data/laboratory
export LOG_LEVEL=INFO
```

## 监控和日志

### 1. 应用监控

#### 添加Actuator依赖
在 `pom.xml` 中添加：
```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-actuator</artifactId>
</dependency>
```

#### 配置监控端点
```properties
# 启用所有监控端点
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.metrics.export.prometheus.enabled=true
```

#### 监控端点访问
- 健康检查: http://localhost:8080/actuator/health
- 应用信息: http://localhost:8080/actuator/info
- 指标数据: http://localhost:8080/actuator/metrics

### 2. 日志管理

#### 日志配置文件 `logback-spring.xml`
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/laboratory.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/laboratory.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

## 性能优化

### 1. JVM调优

```bash
# 生产环境JVM参数
JAVA_OPTS="-Xms1g -Xmx2g \
           -XX:+UseG1GC \
           -XX:MaxGCPauseMillis=200 \
           -XX:+HeapDumpOnOutOfMemoryError \
           -XX:HeapDumpPath=/var/log/laboratory/ \
           -Djava.security.egd=file:/dev/./urandom"
```

### 2. 应用配置优化

```properties
# 连接池配置
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000

# Web服务器配置
server.tomcat.max-threads=200
server.tomcat.min-spare-threads=10
server.tomcat.max-connections=8192

# 压缩配置
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
```

## 安全配置

### 1. 基础安全设置

```properties
# 隐藏服务器信息
server.server-header=
server.error.include-stacktrace=never

# HTTPS配置（生产环境推荐）
server.ssl.enabled=true
server.ssl.key-store=classpath:keystore.p12
server.ssl.key-store-password=your_keystore_password
server.ssl.key-store-type=PKCS12
```

### 2. 防火墙配置

```bash
# Ubuntu/Debian (ufw)
sudo ufw allow 8080/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## 备份和恢复

### 1. 数据备份

```bash
# H2数据库备份脚本
#!/bin/bash
BACKUP_DIR="/backup/laboratory"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
cp -r ./data $BACKUP_DIR/data_$DATE
tar -czf $BACKUP_DIR/laboratory_backup_$DATE.tar.gz $BACKUP_DIR/data_$DATE

# 保留最近30天的备份
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 2. 应用备份

```bash
# 完整应用备份
#!/bin/bash
BACKUP_DIR="/backup/laboratory-app"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/laboratory-app_$DATE.tar.gz \
    --exclude='target' \
    --exclude='logs' \
    --exclude='.git' \
    /opt/laboratory/
```

## 故障排除

### 1. 常见问题诊断

```bash
# 检查Java进程
ps aux | grep java

# 检查端口占用
netstat -tlnp | grep 8080
lsof -i :8080

# 检查磁盘空间
df -h

# 检查内存使用
free -h

# 检查系统负载
top
htop
```

### 2. 日志分析

```bash
# 查看错误日志
grep -i error logs/laboratory.log

# 查看最近的日志
tail -f logs/laboratory.log

# 统计错误数量
grep -c "ERROR" logs/laboratory.log

# 查看特定时间段的日志
sed -n '/2025-08-25 10:00:00/,/2025-08-25 11:00:00/p' logs/laboratory.log
```

## 升级和维护

### 1. 应用升级流程

```bash
# 1. 备份当前版本
./backup.sh

# 2. 停止服务
sudo systemctl stop laboratory

# 3. 更新代码
git pull origin main
mvn clean package

# 4. 替换JAR文件
cp target/laboratory-1.0-SNAPSHOT.jar /opt/laboratory/

# 5. 启动服务
sudo systemctl start laboratory

# 6. 验证服务状态
sudo systemctl status laboratory
curl http://localhost:8080/actuator/health
```

### 2. 定期维护任务

```bash
# 清理日志文件
find logs/ -name "*.log" -mtime +30 -delete

# 清理临时文件
rm -rf /tmp/spring-boot-*

# 检查磁盘使用情况
du -sh /opt/laboratory/

# 更新系统包
sudo apt update && sudo apt upgrade
```

---

通过以上部署和运维指南，您可以在各种环境中成功部署和维护虚拟实验室系统。如有问题，请参考故障排除部分或联系技术支持。
