# 虚拟实验室系统 - 开发者指南

## 开发环境搭建

### 必需工具
- **IDE**: IntelliJ IDEA / Eclipse / VS Code
- **Java**: OpenJDK 11+
- **Maven**: 3.6+
- **Git**: 版本控制
- **Postman**: API测试（可选）

### IDE配置

#### IntelliJ IDEA
1. 导入Maven项目
2. 设置Project SDK为Java 11
3. 启用注解处理器
4. 安装Spring Boot插件

#### VS Code
1. 安装Java Extension Pack
2. 安装Spring Boot Extension Pack
3. 配置Java路径

### 代码规范

#### Java代码规范
- 使用4个空格缩进
- 类名使用PascalCase
- 方法名和变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE
- 包名使用小写字母

#### 注释规范
```java
/**
 * 用户服务类
 * 
 * 提供用户注册、登录等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class UserService {
    
    /**
     * 用户注册
     * 
     * @param username 用户名
     * @param password 密码
     * @return 注册成功的用户对象
     * @throws RuntimeException 当用户名已存在时抛出异常
     */
    public User registerUser(String username, String password) {
        // 实现逻辑
    }
}
```

## 项目架构详解

### 分层架构

```
┌─────────────────┐
│   Presentation  │  ← Controller层 (REST API)
├─────────────────┤
│    Business     │  ← Service层 (业务逻辑)
├─────────────────┤
│   Persistence   │  ← Repository层 (数据访问)
├─────────────────┤
│    Database     │  ← H2数据库
└─────────────────┘
```

### 核心组件

#### 1. Controller层
- **职责**: 处理HTTP请求，参数验证，响应格式化
- **原则**: 薄控制器，不包含业务逻辑
- **示例**:
```java
@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping("/register")
    public ResponseEntity<Map<String, Object>> registerUser(@RequestBody UserRequest request) {
        // 参数验证
        // 调用Service
        // 返回响应
    }
}
```

#### 2. Service层
- **职责**: 业务逻辑处理，事务管理
- **原则**: 单一职责，高内聚低耦合
- **示例**:
```java
@Service
@Transactional
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    public User registerUser(String username, String password) {
        // 业务逻辑验证
        // 数据处理
        // 调用Repository
    }
}
```

#### 3. Repository层
- **职责**: 数据访问，CRUD操作
- **原则**: 简单的数据访问接口
- **示例**:
```java
@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    Optional<User> findByUsername(String username);
    boolean existsByUsername(String username);
}
```

## 开发流程

### 1. 功能开发流程

```mermaid
graph TD
    A[需求分析] --> B[设计API接口]
    B --> C[创建实体类]
    C --> D[实现Repository]
    D --> E[实现Service]
    E --> F[实现Controller]
    F --> G[编写测试]
    G --> H[前端集成]
    H --> I[测试验证]
```

### 2. 代码提交规范

#### Git提交信息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(user): 添加用户注册功能

- 实现用户注册API
- 添加用户名唯一性验证
- 完善错误处理机制

Closes #123
```

## 新功能开发指南

### 1. 添加新的化学反应

#### 步骤1: 扩展反应数据库
```java
// 在ChemicalReactionSimulator.java中添加
static {
    REACTION_DATABASE.put("新反应输入", new ReactionResult("平衡方程式", "安全警告"));
}
```

#### 步骤2: 添加测试用例
```java
@Test
public void testNewChemicalReaction() {
    ChemicalReactionSimulator simulator = new ChemicalReactionSimulator();
    ReactionResult result = simulator.simulateReaction("新反应输入");
    
    assertEquals("期望的平衡方程式", result.getResult());
    assertEquals("期望的安全警告", result.getWarning());
}
```

### 2. 添加新的API端点

#### 步骤1: 定义请求/响应模型
```java
public class NewFeatureRequest {
    private String parameter1;
    private String parameter2;
    
    // getters and setters
}

public class NewFeatureResponse {
    private String result;
    private boolean success;
    
    // getters and setters
}
```

#### 步骤2: 实现Service方法
```java
@Service
public class NewFeatureService {
    
    public NewFeatureResponse processNewFeature(NewFeatureRequest request) {
        // 业务逻辑实现
        return new NewFeatureResponse();
    }
}
```

#### 步骤3: 添加Controller端点
```java
@RestController
@RequestMapping("/api/new-feature")
public class NewFeatureController {
    
    @Autowired
    private NewFeatureService newFeatureService;
    
    @PostMapping("/process")
    public ResponseEntity<NewFeatureResponse> processNewFeature(@RequestBody NewFeatureRequest request) {
        NewFeatureResponse response = newFeatureService.processNewFeature(request);
        return ResponseEntity.ok(response);
    }
}
```

### 3. 数据库扩展

#### 添加新实体
```java
@Entity
@Table(name = "new_entity")
public class NewEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false)
    private String name;
    
    // 构造函数、getters、setters
}
```

#### 创建Repository
```java
@Repository
public interface NewEntityRepository extends JpaRepository<NewEntity, Long> {
    List<NewEntity> findByName(String name);
}
```

## 测试指南

### 1. 单元测试

#### Service层测试示例
```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void testRegisterUser_Success() {
        // Given
        String username = "testuser";
        String password = "password123";
        when(userRepository.existsByUsername(username)).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(new User(username, password));
        
        // When
        User result = userService.registerUser(username, password);
        
        // Then
        assertNotNull(result);
        assertEquals(username, result.getUsername());
        verify(userRepository).save(any(User.class));
    }
    
    @Test
    void testRegisterUser_UserExists() {
        // Given
        String username = "existinguser";
        when(userRepository.existsByUsername(username)).thenReturn(true);
        
        // When & Then
        assertThrows(RuntimeException.class, () -> {
            userService.registerUser(username, "password");
        });
    }
}
```

#### Controller层测试示例
```java
@WebMvcTest(UserController.class)
class UserControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private UserService userService;
    
    @Test
    void testRegisterUser_Success() throws Exception {
        // Given
        User user = new User("testuser", "password123");
        user.setId(1L);
        when(userService.registerUser("testuser", "password123")).thenReturn(user);
        
        // When & Then
        mockMvc.perform(post("/api/users/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content("{\"username\":\"testuser\",\"password\":\"password123\"}"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.message").value("Registration successful"))
                .andExpect(jsonPath("$.userId").value(1));
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(locations = "classpath:application-test.properties")
class LaboratoryApplicationIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    void testUserRegistrationAndLogin() {
        // 测试用户注册
        UserRequest registerRequest = new UserRequest("testuser", "password123");
        ResponseEntity<Map> registerResponse = restTemplate.postForEntity(
            "/api/users/register", registerRequest, Map.class);
        
        assertEquals(HttpStatus.OK, registerResponse.getStatusCode());
        
        // 测试用户登录
        UserRequest loginRequest = new UserRequest("testuser", "password123");
        ResponseEntity<Map> loginResponse = restTemplate.postForEntity(
            "/api/users/login", loginRequest, Map.class);
        
        assertEquals(HttpStatus.OK, loginResponse.getStatusCode());
    }
}
```

### 3. 测试配置

#### application-test.properties
```properties
# 测试数据库配置
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.username=sa
spring.datasource.password=

# JPA配置
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true

# 日志配置
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG
```

## 调试技巧

### 1. 日志调试

#### 添加调试日志
```java
@Service
public class UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserService.class);
    
    public User registerUser(String username, String password) {
        logger.debug("Attempting to register user: {}", username);
        
        if (userRepository.existsByUsername(username)) {
            logger.warn("Registration failed: User {} already exists", username);
            throw new RuntimeException("User already exists");
        }
        
        User user = new User(username, password);
        User savedUser = userRepository.save(user);
        
        logger.info("User {} registered successfully with ID: {}", username, savedUser.getId());
        return savedUser;
    }
}
```

### 2. 断点调试

#### IDE断点设置
1. 在关键代码行设置断点
2. 使用Debug模式启动应用
3. 通过API调用触发断点
4. 检查变量值和执行流程

### 3. 性能分析

#### 使用Spring Boot Actuator
```properties
# 启用性能监控
management.endpoints.web.exposure.include=metrics,health,info
management.endpoint.metrics.enabled=true
```

#### 访问性能指标
- JVM内存使用: `/actuator/metrics/jvm.memory.used`
- HTTP请求统计: `/actuator/metrics/http.server.requests`
- 数据库连接池: `/actuator/metrics/hikaricp.connections`

## 常见问题解决

### 1. 编译问题

#### Maven依赖冲突
```bash
# 查看依赖树
mvn dependency:tree

# 排除冲突依赖
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-web</artifactId>
    <exclusions>
        <exclusion>
            <groupId>conflicting-group</groupId>
            <artifactId>conflicting-artifact</artifactId>
        </exclusion>
    </exclusions>
</dependency>
```

### 2. 运行时问题

#### Bean注入失败
```java
// 确保组件在正确的包路径下
@ComponentScan(basePackages = "org.example")
@SpringBootApplication
public class LaboratoryApplication {
    // ...
}
```

#### 数据库连接问题
```properties
# 检查数据库配置
spring.datasource.url=jdbc:h2:mem:laboratory
spring.datasource.driver-class-name=org.h2.Driver
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
```

### 3. 前端集成问题

#### CORS配置
```java
@CrossOrigin(origins = "*")
@RestController
public class UserController {
    // 或者全局配置
}

@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/api/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE")
                .allowedHeaders("*");
    }
}
```

## 代码质量保证

### 1. 代码检查工具

#### SpotBugs配置
```xml
<plugin>
    <groupId>com.github.spotbugs</groupId>
    <artifactId>spotbugs-maven-plugin</artifactId>
    <version>4.7.3.0</version>
</plugin>
```

#### Checkstyle配置
```xml
<plugin>
    <groupId>org.apache.maven.plugins</groupId>
    <artifactId>maven-checkstyle-plugin</artifactId>
    <version>3.1.2</version>
    <configuration>
        <configLocation>checkstyle.xml</configLocation>
    </configuration>
</plugin>
```

### 2. 代码覆盖率

#### JaCoCo配置
```xml
<plugin>
    <groupId>org.jacoco</groupId>
    <artifactId>jacoco-maven-plugin</artifactId>
    <version>0.8.8</version>
    <executions>
        <execution>
            <goals>
                <goal>prepare-agent</goal>
            </goals>
        </execution>
        <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
                <goal>report</goal>
            </goals>
        </execution>
    </executions>
</plugin>
```

运行覆盖率测试：
```bash
mvn clean test jacoco:report
```

## 扩展建议

### 1. 功能扩展方向
- 用户权限管理
- 实验模板系统
- 实验结果可视化
- 多语言支持
- 移动端适配

### 2. 技术栈升级
- Spring Boot 3.x
- Java 17+
- React/Vue.js前端
- MySQL/PostgreSQL数据库
- Redis缓存
- Docker容器化

### 3. 架构优化
- 微服务架构
- 消息队列
- 分布式缓存
- API网关
- 服务监控

---

通过遵循本开发者指南，您可以高效地参与虚拟实验室系统的开发和维护工作。如有疑问，请参考相关文档或联系项目维护者。
